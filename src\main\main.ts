import { app, BrowserWindow, Menu } from 'electron';
import * as path from 'path';
import { registerIpcHandlers } from './ipc/handlers';
import { databaseService } from './database/database';
import { seedDataService } from './database/seedData';

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;

const isDevelopment = process.env.NODE_ENV === 'development';

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, '../preload/preload.js'),
    },
    icon: path.join(__dirname, '../../assets/icons/app-icon.png'),
    show: false, // Don't show until ready-to-show
    titleBarStyle: 'default',
  });

  // Load the app
  if (isDevelopment) {
    mainWindow.loadURL('http://localhost:3000');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
      
      // Focus on window
      if (isDevelopment) {
        mainWindow.focus();
      }
    }
  });

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    // Dereference the window object
    mainWindow = null;
  });

  // Handle window controls on macOS
  mainWindow.on('close', (event) => {
    if (process.platform === 'darwin') {
      event.preventDefault();
      mainWindow?.hide();
    }
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  try {
    // Initialize database
    console.log('🔧 Initializing database...');
    await databaseService.initialize();

    // Check if data seeding is needed
    const needsSeeding = await seedDataService.isDataSeedingNeeded();
    if (needsSeeding) {
      console.log('🌱 Seeding initial data...');
      await seedDataService.seedAll();
    }

    // Register IPC handlers
    registerIpcHandlers();

    createWindow();
  } catch (error) {
    console.error('❌ Failed to initialize application:', error);
    app.quit();
  }

  // On macOS, re-create window when dock icon is clicked
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });

  // Set application menu
  const template: any[] = [];

  if (process.platform === 'darwin') {
    // macOS menu
    template.push({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideothers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    });
  }

  // File menu (all platforms)
  template.push({
    label: 'File',
    submenu: [
      {
        label: 'New',
        accelerator: 'CmdOrCtrl+N',
        enabled: false // Placeholder for future functionality
      },
      {
        label: 'Open',
        accelerator: 'CmdOrCtrl+O',
        enabled: false // Placeholder for future functionality
      },
      { type: 'separator' },
      process.platform === 'darwin' ? { role: 'close' } : { role: 'quit' }
    ]
  });

  // Edit menu (all platforms)
  template.push({
    label: 'Edit',
    submenu: [
      { role: 'undo' },
      { role: 'redo' },
      { type: 'separator' },
      { role: 'cut' },
      { role: 'copy' },
      { role: 'paste' },
      { role: 'selectall' }
    ]
  });

  // View menu (all platforms)
  template.push({
    label: 'View',
    submenu: [
      { role: 'reload' },
      { role: 'forceReload' },
      { type: 'separator' },
      { role: 'resetZoom' },
      { role: 'zoomIn' },
      { role: 'zoomOut' },
      { type: 'separator' },
      { role: 'togglefullscreen' }
    ]
  });

  // Developer menu (all platforms)
  template.push({
    label: 'Developer',
    submenu: [
      {
        label: 'Toggle Developer Tools',
        accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
        click: () => {
          const focusedWindow = BrowserWindow.getFocusedWindow();
          if (focusedWindow) {
            if (focusedWindow.webContents.isDevToolsOpened()) {
              focusedWindow.webContents.closeDevTools();
            } else {
              focusedWindow.webContents.openDevTools();
            }
          }
        }
      },
      {
        label: 'Open Developer Tools',
        accelerator: 'F12',
        click: () => {
          const focusedWindow = BrowserWindow.getFocusedWindow();
          if (focusedWindow) {
            focusedWindow.webContents.openDevTools();
          }
        }
      },
      { type: 'separator' },
      {
        label: 'Reload',
        accelerator: 'CmdOrCtrl+R',
        click: () => {
          const focusedWindow = BrowserWindow.getFocusedWindow();
          if (focusedWindow) {
            focusedWindow.reload();
          }
        }
      },
      {
        label: 'Force Reload',
        accelerator: 'CmdOrCtrl+Shift+R',
        click: () => {
          const focusedWindow = BrowserWindow.getFocusedWindow();
          if (focusedWindow) {
            focusedWindow.webContents.reloadIgnoringCache();
          }
        }
      }
    ]
  });

  // Help menu (all platforms)
  template.push({
    label: 'Help',
    submenu: [
      {
        label: 'About मैथिली विकास कोष Shop Management',
        click: () => {
          // TODO: Show about dialog
          console.log('About dialog - TODO');
        }
      },
      {
        label: 'Learn More',
        click: () => {
          // TODO: Open documentation
          console.log('Documentation - TODO');
        }
      }
    ]
  });

  Menu.setApplicationMenu(Menu.buildFromTemplate(template));
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    // Close database connection before quitting
    databaseService.close();
    app.quit();
  }
});

// Handle app quit
app.on('before-quit', () => {
  // Close database connection
  databaseService.close();
});

// Security: Prevent new window creation
app.on('web-contents-created', (_event, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    // Prevent opening new windows
    console.log('Blocked attempt to open:', url);
    return { action: 'deny' };
  });
});
